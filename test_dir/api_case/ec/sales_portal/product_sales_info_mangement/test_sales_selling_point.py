# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesSellingPoint(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_selling_point(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-Selling Point商品卖点-查询Selling Point页面数据"""
        selling_point_list = CentralIm().selling_point_list(headers=sales_header)
        assert selling_point_list["result"] is True, f'查询Selling Point页面数据异常{selling_point_list}'
        assert len(selling_point_list["object"]["data"]) > 0, f'查询Selling Point页面数据异常{selling_point_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_selling_point_with_product_id')
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_selling_point_with_product_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-Selling Point商品卖点-根据商品ID查询Selling Point数据"""
        product_id = "88235"  # 测试商品ID
        selling_point_list = CentralIm().selling_point_list(
            headers=sales_header,
            product_id=product_id,
            pageSize=20,
            startColumn=0
        )
        assert selling_point_list["result"] is True, f'根据商品ID查询Selling Point数据异常{selling_point_list}'
        assert "data" in selling_point_list["object"], f'返回数据为空，请检查或修改商品ID{selling_point_list}'

