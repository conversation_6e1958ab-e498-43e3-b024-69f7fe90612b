# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesSynonyms(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_synonyms(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-同义词管理-查询同义词管理页面数据"""
        synonym_list = CentralIm().search_synonym_list(headers=sales_header)

        assert len(synonym_list["object"]["data"]) > 0, f'查询同义词管理页面数据为空或有异常{synonym_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_synonym_list_with_all_params')
    def test_search_synonym_list_with_all_params(self, sales_header):
        """商品销售信息管理-商品内容信息管理-同义词管理-测试所有查询条件组合在一起"""
        synonym_list = CentralIm().search_synonym_list(
            headers=sales_header,
            pageSize=20,
            page=1,
            status=False,  # ENABLE
            keyword="test",
            type="Y",  # EQUIVALENT
            language="zh"  # 中文
        )

        assert synonym_list["result"] == True, f'查询同义词列表失败: {synonym_list}'
        assert "object" in synonym_list, f'响应缺少object字段: {synonym_list}'
        assert synonym_list.object.data, f'响应缺少data字段: {synonym_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_synonym_list_with_language')
    def test_search_synonym_list_with_language(self, sales_header):
        """商品销售信息管理-商品内容信息管理-同义词管理-测试语言参数"""
        # 测试不同语言类型
        languages = ["en", "zh", "ja", "ko"]

        for language in languages:
            synonym_list = CentralIm().search_synonym_list(
                headers=sales_header,
                page=1,
                pageSize=20,
                language=language
            )

            assert synonym_list["result"] == True, f'查询语言{language}的同义词列表失败: {synonym_list}'
            assert len(synonym_list["object"]["data"]) > 0, f'根据语言{language}查询同义词管理页面数据为空或有异常{synonym_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_synonym_list_with_status')
    def test_search_synonym_list_with_status(self, sales_header):
        """商品销售信息管理-商品内容信息管理-同义词管理-测试状态参数"""
        # 测试不同状态
        statuses = [False, True]  # False:ENABLE, True:DISABLE

        for status in statuses:
            status_name = "ENABLE" if status == False else "DISABLE"
            synonym_list = CentralIm().search_synonym_list(
                headers=sales_header,
                page=1,
                pageSize=20,
                status=status
            )

            assert synonym_list["result"] == True, f'查询状态{status_name}的同义词列表失败: {synonym_list}'
            assert len(synonym_list["object"]["data"]) > 0, f'根据状态{status_name}查询同义词管理页面数据为空或有异常{synonym_list}'


    @weeeTest.mark.list('sales', 'Transaction', 'test_search_synonym_list_with_type')
    def test_search_synonym_list_with_type(self, sales_header):
        """商品销售信息管理-商品内容信息管理-同义词管理-测试Relation参数"""
        # 测试不同类型
        types = ["Y", "N"]  # Y:EQUIVALENT, N:MAPPING

        for synonym_type in types:
            type_name = "EQUIVALENT" if synonym_type == "Y" else "MAPPING"
            synonym_list = CentralIm().search_synonym_list(
                headers=sales_header,
                page=1,
                pageSize=20,
                type=synonym_type
            )

            assert synonym_list["result"] == True, f'查询类型{type_name}的同义词列表失败: {synonym_list}'
            assert len(synonym_list["object"]["data"]) > 0, f'根据类型{type_name}查询同义词管理页面数据为空或有异常{synonym_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_search_synonym_list_with_keyword')
    def test_search_synonym_list_with_keyword(self, sales_header):
        """商品销售信息管理-商品内容信息管理-同义词管理-测试关键词搜索"""
        # 测试关键词搜索
        synonym_list = CentralIm().search_synonym_list(
            headers=sales_header,
            page=1,
            pageSize=20,
            keyword="test"
        )

        assert synonym_list["result"] == True, f'关键词搜索同义词列表失败: {synonym_list}'
        assert len(synonym_list["object"]["data"]) > 0, f'根据关键词查询同义词管理页面数据为空或有异常{synonym_list}'

