# !/usr/bin/python3
# -*- coding: utf-8 -*-
import pytest
import weeeTest

from test_dir.api.ec.central_portal.central_recommend import CentralRecommend


class TestSalesRecommendRules(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_recommend_rules')
    @weeeTest.mark.list('sales', 'Transaction', 'sales_portal', 'sales_portal_tb1')
    def test_sales_recommend_rules(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品推荐规则-查询商品推荐规则页面数据"""
        flow = ["retrieval", "ranking", "re-ranking"]
        for flow in flow:
            recommend_trace_product_list = CentralRecommend().recommend_trace_product_list(headers=sales_header,
                                                                                           flow=flow)

            assert len(recommend_trace_product_list[
                           "object"]) > 0, f'查询商品推荐规则页面数据异常{recommend_trace_product_list}'
